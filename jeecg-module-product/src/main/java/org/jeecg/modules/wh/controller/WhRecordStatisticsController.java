package org.jeecg.modules.wh.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.modules.wh.service.IWhRecordStatisticsService;
import org.jeecg.modules.wh.dto.WhRecordStatisticsDTO;
import org.jeecg.modules.wh.vo.WhRecordStatisticsVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * @Description: 用户门户记录统计表
 * @Author: jeecg-boot
 * @Date: 2024-12-19
 * @Version: V1.0
 */
@Api(tags="用户门户记录统计管理")
@RestController
@RequestMapping("/wh/recordStatistics")
@Slf4j
public class WhRecordStatisticsController {

    @Autowired
    private IWhRecordStatisticsService whRecordStatisticsService;

    /**
     * 增加记录统计数量
     *
     * @param dto 统计参数
     * @return
     */
    @ApiOperation(value="增加记录统计数量", notes="将指定内容和记录类型的数量+1")
    @PostMapping(value = "/increment")
    public Result<String> incrementCount(@Valid @RequestBody WhRecordStatisticsDTO dto) {
        try {
            boolean success = whRecordStatisticsService.incrementCount(dto);
            if (success) {
                return Result.OK("操作成功");
            } else {
                return Result.error("操作失败");
            }
        } catch (Exception e) {
            log.error("增加统计数量失败", e);
            return Result.error("操作失败：" + e.getMessage());
        }
    }

    /**
     * 根据内容ID获取统计信息
     *
     * @param pid 内容ID
     * @return
     */
    @ApiOperation(value="获取统计信息", notes="根据内容ID查询4个记录类型的统计数量")
    @GetMapping(value = "/getStatistics")
    public Result<WhRecordStatisticsVO> getStatisticsByPid(@RequestParam(name="pid", required=true) String pid) {
        try {
            WhRecordStatisticsVO vo = whRecordStatisticsService.getStatisticsByPid(pid);
            return Result.OK(vo);
        } catch (Exception e) {
            log.error("查询统计信息失败", e);
            return Result.error("查询失败：" + e.getMessage());
        }
    }
}
