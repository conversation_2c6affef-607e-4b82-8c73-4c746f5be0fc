package org.jeecg.modules.wh.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.wh.entity.WhRecord;
import org.jeecg.modules.wh.mapper.WhRecordMapper;
import org.jeecg.modules.wh.service.IWhRecordService;
import org.jeecg.modules.wh.dto.WhRecordQueryDTO;
import org.jeecg.modules.wh.dto.WhRecordSaveDTO;
import org.jeecg.modules.wh.vo.WhRecordVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * @Description: 用户门户记录表
 * @Author: jeecg-boot
 * @Date: 2024-12-19
 * @Version: V1.0
 */
@Service
@Slf4j
public class WhRecordServiceImpl extends ServiceImpl<WhRecordMapper, WhRecord> implements IWhRecordService {


    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveUserRecord(WhRecordSaveDTO saveDTO) {
        try {
            // 检查记录是否已存在
            WhRecord existRecord = this.baseMapper.findExistRecord(
                saveDTO.getUserId(),
                saveDTO.getContentId(),
                saveDTO.getContentType(),
                saveDTO.getRecordType()
            );

            if (existRecord != null) {
                // 更新现有记录的时间
                existRecord.setUpdateTime(new Date());
                this.updateById(existRecord);
                log.info("更新用户记录成功，用户ID：{}，内容ID：{}，记录类型：{}",
                    saveDTO.getUserId(), saveDTO.getContentId(), saveDTO.getRecordType());
            } else {
                // 创建新记录
                WhRecord record = new WhRecord();
                record.setUserId(saveDTO.getUserId());
                record.setContentId(saveDTO.getContentId());
                record.setContentType(saveDTO.getContentType());
                record.setRecordType(saveDTO.getRecordType());
                record.setTenantId(0); // 默认租户ID为0
                record.setCreateBy(saveDTO.getUserId());
                record.setCreateTime(new Date());
                record.setUpdateTime(new Date());
                this.save(record);
                log.info("添加用户记录成功，用户ID：{}，内容ID：{}，记录类型：{}",
                    saveDTO.getUserId(), saveDTO.getContentId(), saveDTO.getRecordType());
            }
            return true;
        } catch (Exception e) {
            log.error("保存用户记录失败，参数：{}", saveDTO, e);
            throw new RuntimeException("保存用户记录失败", e);
        }
    }

    @Override
    public List<WhRecordVO> getRecordListWithTitle(WhRecordQueryDTO queryDTO) {
        try {
            return this.baseMapper.getRecordListWithTitle(queryDTO);
        } catch (Exception e) {
            log.error("查询用户记录列表失败，查询条件：{}", queryDTO, e);
            throw new RuntimeException("查询用户记录列表失败", e);
        }
    }
}
