<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>太⼀企业管理有限公司</title>
    <!-- favicons Icons -->




    <meta name="description" content="Insur HTML 5 Template ">

    <!-- fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com/">

    <link rel="preconnect" href="https://fonts.gstatic.com/" crossorigin="">

    <link href="static/css/css2.css" rel="stylesheet">


    <link rel="stylesheet" href="static/css/bootstrap.min.css">
    <link rel="stylesheet" href="static/css/animate.min.css">
    <link rel="stylesheet" href="static/css/custom-animate.css">
    <link rel="stylesheet" href="static/css/all.min.css">
    <link rel="stylesheet" href="static/css/jarallax.css">
    <link rel="stylesheet" href="static/css/jquery.magnific-popup.css">
    <link rel="stylesheet" href="static/css/nouislider.min.css">
    <link rel="stylesheet" href="static/css/nouislider.pips.css">
    <link rel="stylesheet" href="static/css/odometer.min.css">
    <link rel="stylesheet" href="static/css/swiper.min.css">
    <link rel="stylesheet" href="static/css/style.css">
    <link rel="stylesheet" href="static/css/style1.css">
    <link rel="stylesheet" href="static/css/tiny-slider.min.css">
    <link rel="stylesheet" href="static/css/stylesheet.css">
    <link rel="stylesheet" href="static/css/owl.carousel.min.css">
    <link rel="stylesheet" href="static/css/owl.theme.default.min.css">
    <link rel="stylesheet" href="static/css/jquery.bxslider.css">
    <link rel="stylesheet" href="static/css/bootstrap-select.min.css">
    <link rel="stylesheet" href="static/css/vegas.min.css">
    <link rel="stylesheet" href="static/css/jquery-ui.css">
    <link rel="stylesheet" href="static/css/timePicker.css">

    <!-- template styles -->
    <link rel="stylesheet" href="static/css/insur.css">
    <link rel="stylesheet" href="static/css/insur-responsive.css">

    <!-- 自定义样式 -->
    <style>
        .interaction-item.liked .interaction-icon {
            background-color: #ff6b6b !important;
            color: white !important;
        }

        .interaction-item.favorited .interaction-icon {
            background-color: #ffd93d !important;
            color: white !important;
        }
    </style>
</head>

<body class="custom-cursor">

    <div class="custom-cursor__cursor"></div>
    <div class="custom-cursor__cursor-two"></div>





    <div class="preloader">
        <div class="preloader__image"></div>
    </div>
    <!-- /.preloader -->

    <div id="app">
        <div class="page-wrapper">
            <header class="main-header clearfix">
                <div class="main-header__top">
                    <div class="container">
                        <div class="main-header__top-inner">
                            <div class="main-header__top-address">
                                <ul class="list-unstyled main-header__top-address-list">
                                    <li>
                                        <i class="icon">
                                            <span class="icon-pin"></span>
                                        </i>
                                        <div class="text">
                                            <p>{{companyInfo.address}}</p>
                                        </div>
                                    </li>
                                    <li>
                                        <i class="icon">
                                            <span class="icon-email"></span>
                                        </i>
                                        <div class="text">
                                            <p><a href="mailto:<EMAIL>">{{companyInfo.mailbox}}</a></p>
                                        </div>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
                <nav class="main-menu clearfix">
                    <div class="main-menu__wrapper clearfix">
                        <div class="container">
                            <div class="main-menu__wrapper-inner clearfix">
                                <div class="main-menu__left">
                                    <div class="main-menu__logo">
                                        <a><img :src="companyInfo.logoImg" alt="" style="height: 27px"></a>
                                    </div>
                                    <div class="main-menu__main-menu-box">
                                        <div class="main-menu__main-menu-box-inner">
                                            <a href="#" class="mobile-nav__toggler" @click="expanded = true"><i
                                                    class="fa fa-bars"></i></a>
                                            <ul class="main-menu__list one-page-scroll-menu">
                                                <li class=" megamenu scrollToLink">
                                                    <a href="index-one-page.html">首页 </a>
                                                </li>

                                                <li class="scrollToLink ">
                                                    <a href="company.html">保险公司 </a>
                                                </li>
                                                <li class="scrollToLink">
                                                    <a href="products.html">保险产品</a>
                                                </li>
                                                <li class="scrollToLink current">
                                                    <a href="news.html">新闻资讯</a>
                                                </li>
                                                <li class="scrollToLink" style="margin-right: 37px;">
                                                    <a href="about.html">关于我们</a>
                                                </li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </nav>
            </header>

            <div class="stricky-header stricked-menu main-menu">
                <div class="sticky-header__content"></div><!-- /.sticky-header__content -->
            </div><!-- /.stricky-header -->



            <!--Page Header Start-->
            <section class="page-header">
                <div class="page-header-bg" style="background-image: url(assets/images/backgrounds/page-header-bg.jpg)">
                </div>
                <div class="page-header-shape-1"><img src="static/picture/page-header-shape-1.png" alt=""></div>
                <div class="container">
                    <div class="page-header__inner">
                        <!-- <ul class="thm-breadcrumb list-unstyled">
                        <li><a>Home</a></li>
                        <li><span>/</span></li>
                        <li>news</li>
                    </ul> -->
                        <h2>资讯详情</h2>
                    </div>
                </div>
            </section>
            <!--Page Header End-->

            <!--News One Start-->
            <section class="news-details">
                <div class="container">
                    <ul class="list-unstyled news-details__meta">
                        <li v-if="newsDetail.newsTime"><a href="#"><i class="far fa-calendar"></i>{{newsDetail.newsTime}}</a>
                        </li>
                        <li><a href="#"><i class="far fa-eye" style="color: #015fc9"></i> {{newsDetail.clicksNum || 0}} 查看</a>
                        </li>
                    </ul>
                    <h3 class="news-details__title">{{newsDetail.name}}</h3>
                    <div v-html="newsDetail.content"></div>

                    <!-- 互动按钮区域 -->
                    <div class="news-interaction" style="margin-top: 30px; padding: 20px 0; border-top: 1px solid #e5e5e5;">
                        <div class="interaction-buttons" style="display: flex; justify-content: center; gap: 40px;">
                            <!-- 点赞按钮 -->
                            <div class="interaction-item" :class="{'liked': isLiked}" style="display: flex; flex-direction: column; align-items: center; cursor: pointer;" @click="handleLike">
                                <div class="interaction-icon" style="width: 50px; height: 50px; border-radius: 50%; background-color: #f8f9fa; display: flex; align-items: center; justify-content: center; margin-bottom: 8px; transition: all 0.3s ease; color: #666;">
                                    <i class="fas fa-thumbs-up" style="font-size: 20px;"></i>
                                </div>
                                <span style="font-size: 14px; color: #666;">点赞 {{newsDetail.likeCount || 0}}</span>
                            </div>

                            <!-- 收藏按钮 -->
                            <div class="interaction-item" :class="{'favorited': isFavorited}" style="display: flex; flex-direction: column; align-items: center; cursor: pointer;" @click="handleFavorite">
                                <div class="interaction-icon" style="width: 50px; height: 50px; border-radius: 50%; background-color: #f8f9fa; display: flex; align-items: center; justify-content: center; margin-bottom: 8px; transition: all 0.3s ease; color: #666;">
                                    <i class="fas fa-star" style="font-size: 20px;"></i>
                                </div>
                                <span style="font-size: 14px; color: #666;">收藏 {{newsDetail.favoriteCount || 0}}</span>
                            </div>

                            <!-- 分享按钮 -->
                            <div class="interaction-item" style="display: flex; flex-direction: column; align-items: center; cursor: pointer;">
                                <div class="interaction-icon" style="width: 50px; height: 50px; border-radius: 50%; background-color: #f8f9fa; display: flex; align-items: center; justify-content: center; margin-bottom: 8px; transition: all 0.3s ease; color: #666;">
                                    <i class="fas fa-share-alt" style="font-size: 20px;"></i>
                                </div>
                                <span style="font-size: 14px; color: #666;">分享</span>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- <div class="services-one__bottom">
                    <div class="services-one__container">
                        <div class="row">
                            <div class="col-xl-3 col-lg-4 col-md-6 wow fadeInUp" data-wow-delay="100ms"
                                v-for="item in businessPartner" :key="item">
                                <div class="services-one__single">
                                    <div class="service-one__img">
                                        <img :src="item.img" alt="">
                                    </div>
                                    <div class="service-one__content">
                                        <h2 class="service-one__title" style="height: 60px"><a target="_blank"
                                                :href="item.firmUrl">{{item.name}}</a></h2>
                                        <p class="service-one__text">成立时间：{{item.registrationYear}}</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div> -->
            </section>
            <!--News One End-->

            <!--Site Footer Start-->
            <footer class="site-footer">
                <div class="site-footer-bg" style="background-image: url(static/image/site-footer-bg.png);">
                </div>
                <div class="container">
                    <div class="site-footer__top">
                        <div class="row">
<!--                            <div class="col-xl-4 col-lg-8 col-md-8 wow fadeInUp" data-wow-delay="100ms">-->
<!--                                <div class="footer-widget__column footer-widget__about">-->
<!--                                    <div class="footer-widget__logo">-->
<!--                                        <a><img :src="companyInfo.logoImg" alt="" style="height: 34px"></a>-->
<!--                                    </div>-->
<!--                                </div>-->
<!--                            </div>-->
                            <div class="col-xl-4 col-lg-8 col-md-8 wow fadeInUp" data-wow-delay="200ms">
                                <div class="footer-widget__column footer-widget__contact clearfix">
                                    <h3 class="footer-widget__title">公司地址</h3>
                                    <ul class="footer-widget__contact-list list-unstyled clearfix">
                                        <li>
                                            <div class="icon">
                                                <span class="icon-pin"></span>
                                            </div>
                                            <div class="text">
                                                <p>{{companyInfo.address}}</p>
                                            </div>
                                        </li>
                                    </ul>
                                    <div class="footer-widget__open-hour">
                                        <h3 class="footer-widget__open-hour-title">工作时间</h3>
                                        <h3 class="footer-widget__open-hour-text">工作日 早上9:00-18:00</h3>
                                    </div>
                                </div>
                            </div>
                            <div class="col-xl-4 col-lg-8 col-md-8 wow fadeInUp" data-wow-delay="400ms">
                                <div class="footer-widget__column footer-widget__newsletter">
                                    <h3 class="footer-widget__title">联系电话</h3>
                                    <div class="footer-widget__phone">
                                        <div class="footer-widget__phone-icon">
                                            <span class="icon-telephone"></span>
                                        </div>
                                        <div class="footer-widget__phone-text">
                                            <a href="tel:9200368090">{{companyInfo.phone}}</a>
                                            <p>欢迎拨打</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="site-footer__bottom">
                        <div class="row">
                            <div class="col-xl-12">
                                <div class="site-footer__bottom-inner">
                                    <p class="site-footer__bottom-text">{{companyInfo.copyright}} <a target="_blank"
                                            href="https://beian.miit.gov.cn/#/Integrated/index">{{companyInfo.icpNumber}}</a>
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </footer>
            <!--Site Footer End-->


        </div><!-- /.page-wrapper -->


        <div class="mobile-nav__wrapper" :class="{expanded: expanded}">
            <div class="mobile-nav__overlay mobile-nav__toggler"></div>
            <!-- /.mobile-nav__overlay -->
            <div class="mobile-nav__content">
                <span class="mobile-nav__close mobile-nav__toggler" @click="expanded = false"><i
                        class="fa fa-times"></i></span>

                <div class="logo-box">
                    <a aria-label="logo image"><img src="static/picture/logo-2.png" width="143"
                            alt=""></a>
                </div>
                <!-- /.logo-box -->
                <div class="mobile-nav__container"></div>
                <!-- /.mobile-nav__container -->



            </div>
            <!-- /.mobile-nav__content -->
        </div>
    </div>
    <!-- /.mobile-nav__wrapper -->

    <div class="search-popup">
        <div class="search-popup__overlay search-toggler"></div>
        <!-- /.search-popup__overlay -->
        <div class="search-popup__content">
            <form action="#">
                <label for="search" class="sr-only">search here</label><!-- /.sr-only -->
                <input type="text" id="search" placeholder="Search Here...">
                <button type="submit" aria-label="search submit" class="thm-btn">
                    <i class="icon-magnifying-glass"></i>
                </button>
            </form>
        </div>
        <!-- /.search-popup__content -->
    </div>
    <!-- /.search-popup -->

    <a href="#" data-target="html" class="scroll-to-target scroll-to-top"><i class="fa fa-angle-up"></i></a>


    <script src="static/js/jquery-3.6.0.min.js"></script>
    <script src="static/js/bootstrap.bundle.min.js"></script>
    <script src="static/js/jarallax.min.js"></script>
    <script src="static/js/jquery.ajaxchimp.min.js"></script>
    <script src="static/js/jquery.appear.min.js"></script>
    <script src="static/js/jquery.circle-progress.min.js"></script>
    <script src="static/js/jquery.magnific-popup.min.js"></script>
    <script src="static/js/jquery.validate.min.js"></script>
    <script src="static/js/nouislider.min.js"></script>
    <script src="static/js/odometer.min.js"></script>
    <script src="static/js/swiper.min.js"></script>
    <script src="static/js/tiny-slider.min.js"></script>
    <script src="static/js/wNumb.min.js"></script>
    <script src="static/js/wow.js"></script>
    <script src="static/js/isotope.js"></script>
    <script src="static/js/countdown.min.js"></script>
    <script src="static/js/owl.carousel.min.js"></script>
    <script src="static/js/jquery.bxslider.min.js"></script>
    <script src="static/js/bootstrap-select.min.js"></script>
    <script src="static/js/vegas.min.js"></script>
    <script src="static/js/jquery-ui.js"></script>
    <script src="static/js/timePicker.js"></script>
    <script src="static/js/jquery.circleType.js"></script>
    <script src="static/js/jquery.lettering.min.js"></script>




    <!-- template js -->
    <script src="static/js/insur.js"></script>

    <script src="static/js/vue.js"></script>
    <script src="static/js/api-config.js"></script>
    <script>
      //const jeeApi = "https://www.taiyibx.com";
      //const jeeApi = "http://*************:8080";
        new Vue({
            el: '#app',
            data: {
                expanded: false,
                bannerList: [],
                newsList: [],
                businessPartner: [],
                recommendList: [],
                companyInfo: {},
                newsDetail: {},
                keyword: '',
                isLiked: false,
                isFavorited: false,
            },
            mounted() {
                // this.getBanner()
                this.getNews()
                this.getBusinessPartner()
                this.getRecommend()
                this.getCompanyInfo()
            },
            methods: {
                // 获取轮播
                async getBanner() {
                    const response = await fetch(jeeApi + '/jeecg-boot/api/firmInformation/whCarousel/list', {
                        method: 'get',
                    })
                    const data = await response.json();
                    this.bannerList = data.result.records
                    this.$nextTick(() => {
                        window.thmSwiperInit();
                    })

                },
                // 获取新闻
                async getNews() {
                    // 获取当前URL
                    const url = window.location.href;

                    // 使用URLSearchParams来解析URL中的参数
                    const params = new URLSearchParams(url.split('?')[1]);

                    // 获取id参数的值
                    const id = params.get('id');
                    const response = await fetch(jeeApi + '/jeecg-boot/api/wechat/appNews/queryById?id=' + id, {
                        method: 'get',
                    })
                    const data = await response.json();
                    // this.newsList = data.result.records
                    this.newsDetail = data.result

                },
                // 合作伙伴
                async getBusinessPartner() {
                    const response = await fetch(jeeApi + '/jeecg-boot/api/firmInformation/firmInformation/list?name=' + this.keyword, {
                        method: 'get',
                    })
                    const data = await response.json();
                    this.businessPartner = data.result.records

                },
                // 推荐
                async getRecommend() {
                    const response = await fetch(jeeApi + '/jeecg-boot/api/firmInformation/whProducts/list', {
                        method: 'get',
                    })
                    const data = await response.json();
                    this.recommendList = data.result.records

                },
                // 推荐
                async getCompanyInfo() {
                    const response = await fetch(jeeApi + '/jeecg-boot/api/firmInformation/whHome/getOne', {
                        method: 'get',
                    })
                    const data = await response.json();
                    this.companyInfo = data.result

                },
                // 点赞功能
                async handleLike() {
                    if (this.isLiked) {
                        // 如果已经点赞，可以选择不允许取消点赞，或者实现取消点赞逻辑
                        return;
                    }

                    try {
                        // 获取当前URL中的id参数
                        const url = window.location.href;
                        const params = new URLSearchParams(url.split('?')[1]);
                        const id = params.get('id');

                        const response = await fetch(jeeApi + '/jeecg-boot/wh/recordStatistics/increment', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json'
                            },
                            body: JSON.stringify({
                                pid: id,
                                recordType: 3  // 3表示点赞记录
                            })
                        });

                        const data = await response.json();
                        if (data.success) {
                            this.isLiked = true;
                            // 更新点赞数量
                            this.newsDetail.likeCount = (this.newsDetail.likeCount || 0) + 1;
                        } else {
                            console.error('点赞失败:', data.message);
                        }
                    } catch (error) {
                        console.error('点赞请求失败:', error);
                    }
                },
                // 收藏功能
                async handleFavorite() {
                    if (this.isFavorited) {
                        // 如果已经收藏，可以选择不允许取消收藏，或者实现取消收藏逻辑
                        return;
                    }

                    try {
                        // 获取当前URL中的id参数
                        const url = window.location.href;
                        const params = new URLSearchParams(url.split('?')[1]);
                        const id = params.get('id');

                        const response = await fetch(jeeApi + '/jeecg-boot/wh/recordStatistics/increment', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json'
                            },
                            body: JSON.stringify({
                                pid: id,
                                recordType: 2  // 2表示收藏记录
                            })
                        });

                        const data = await response.json();
                        if (data.success) {
                            this.isFavorited = true;
                            // 更新收藏数量
                            this.newsDetail.favoriteCount = (this.newsDetail.favoriteCount || 0) + 1;
                        } else {
                            console.error('收藏失败:', data.message);
                        }
                    } catch (error) {
                        console.error('收藏请求失败:', error);
                    }
                },
            }
        })
    </script>
</body>

</html>
